<?php
/**
 * Plugin Name: Boss SEO
 * Plugin URI: https://bossseo.com
 * Description: Un plugin SEO avancé avec intelligence artificielle pour WordPress
 * Version: 1.1.0
 * Author: Boss SEO Team
 * Author URI: https://bossseo.com
 * Text Domain: boss-seo
 * Domain Path: /languages
 */

// Si ce fichier est appelé directement, on sort.
if (!defined('WPINC')) {
    die;
}

// Définition des constantes
define('BOSS_SEO_VERSION', '1.1.0');
define('BOSS_SEO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BOSS_SEO_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BOSS_SEO_PLUGIN_FILE', __FILE__);

/**
 * Le code qui s'exécute lors de l'activation du plugin.
 * Cette action est documentée dans includes/class-boss-seo-activator.php
 */
function activate_boss_seo() {
    require_once plugin_dir_path(__FILE__) . 'includes/class-boss-seo-activator.php';
    Boss_SEO_Activator::activate();
}

/**
 * Le code qui s'exécute lors de la désactivation du plugin.
 * Cette action est documentée dans includes/class-boss-seo-deactivator.php
 */
function deactivate_boss_seo() {
    require_once plugin_dir_path(__FILE__) . 'includes/class-boss-seo-deactivator.php';
    Boss_SEO_Deactivator::deactivate();
}

register_activation_hook(__FILE__, 'activate_boss_seo');
register_deactivation_hook(__FILE__, 'deactivate_boss_seo');

/**
 * La classe principale du plugin.
 */
require plugin_dir_path(__FILE__) . 'includes/class-boss-seo.php';

/**
 * Commence l'exécution du plugin.
 *
 * Depuis que tout dans le plugin est enregistré via des hooks,
 * alors lancer le plugin depuis ce point dans le fichier ne va
 * affecter la page de chargement.
 *
 * @since    1.1.0
 */
function run_boss_seo() {
    $plugin = new Boss_SEO();
    $plugin->run();
}

// Retarder l'initialisation jusqu'à ce que WordPress soit complètement chargé
add_action( 'init', 'run_boss_seo', 0 );

/**
 * Initialiser le module Analytics après WordPress
 */
add_action( 'init', function() {
    require_once plugin_dir_path(__FILE__) . 'includes/class-boss-analytics-init.php';
}, 1 );
