<?php
/**
 * Gestionnaire central du cache pour Boss SEO.
 *
 * Cette classe gère de manière unifiée tous les types de cache du plugin :
 * - Cache des assets (CSS/JS avec versioning intelligent)
 * - Cache des données (transients, wp_cache)
 * - Cache des modules (Optimizer, Analytics, etc.)
 * - Intégration avec les plugins de cache tiers
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Gestionnaire central du cache pour Boss SEO.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Cache_Manager {

    /**
     * Instance unique de la classe (Singleton).
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_Cache_Manager    $instance    Instance unique de la classe.
     */
    private static $instance = null;

    /**
     * Version actuelle du cache des assets.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $assets_version    Version du cache des assets.
     */
    private $assets_version = null;

    /**
     * Préfixe pour les clés de cache.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $cache_prefix    Préfixe pour les clés de cache.
     */
    private $cache_prefix = 'boss_seo_';

    /**
     * Liste des modules avec cache.
     *
     * @since    1.1.0
     * @access   private
     * @var      array    $cached_modules    Liste des modules avec cache.
     */
    private $cached_modules = array(
        'optimizer',
        'analytics',
        'ecommerce',
        'technical',
        'local',
        'structured_schemas'
    );

    /**
     * Constructeur privé pour le pattern Singleton.
     *
     * @since    1.1.0
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * Récupère l'instance unique de la classe.
     *
     * @since    1.1.0
     * @return   Boss_Cache_Manager    Instance unique de la classe.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialise les hooks WordPress.
     *
     * @since    1.1.0
     */
    private function init_hooks() {
        // Vérifier que WordPress est chargé avant d'ajouter les hooks
        if ( ! function_exists( 'add_action' ) ) {
            return;
        }

        // Hook pour invalider le cache lors des mises à jour de contenu
        add_action( 'save_post', array( $this, 'invalidate_post_cache' ) );
        add_action( 'delete_post', array( $this, 'invalidate_post_cache' ) );

        // Hook pour invalider le cache lors des mises à jour de paramètres
        add_action( 'update_option', array( $this, 'maybe_invalidate_settings_cache' ), 10, 3 );

        // Hook pour invalider le cache lors de l'activation/désactivation de plugins
        add_action( 'activated_plugin', array( $this, 'invalidate_all_cache' ) );
        add_action( 'deactivated_plugin', array( $this, 'invalidate_all_cache' ) );

        // Hook pour invalider le cache lors des mises à jour de thème
        add_action( 'switch_theme', array( $this, 'invalidate_assets_cache' ) );

        // Désactiver le cache en mode debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            add_action( 'init', array( $this, 'disable_cache_in_debug_mode' ) );
        }
    }

    /**
     * Récupère la version actuelle des assets avec cache busting intelligent.
     *
     * @since    1.1.0
     * @return   string    Version des assets.
     */
    public function get_assets_version() {
        if ( null === $this->assets_version ) {
            $this->assets_version = $this->generate_assets_version();
        }
        return $this->assets_version;
    }

    /**
     * Génère une version des assets basée sur la dernière modification.
     *
     * @since    1.1.0
     * @return   string    Version générée.
     */
    private function generate_assets_version() {
        // En mode debug, utiliser un timestamp pour forcer le rechargement
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            return time();
        }

        // Vérifier s'il y a une version forcée
        $forced_version = get_option( $this->cache_prefix . 'forced_assets_version' );
        if ( $forced_version ) {
            return $forced_version;
        }

        // Utiliser la version du plugin par défaut
        if ( defined( 'BOSS_SEO_VERSION' ) ) {
            return BOSS_SEO_VERSION;
        }

        return '1.1.0';
    }

    /**
     * Force le rechargement de tous les assets.
     *
     * @since    1.1.0
     * @return   bool    True si le rechargement a été forcé.
     */
    public function force_assets_refresh() {
        $new_version = time();
        update_option( $this->cache_prefix . 'forced_assets_version', $new_version );
        $this->assets_version = $new_version;

        // Log de l'action
        error_log( 'Boss SEO: Cache des assets forcé à la version ' . $new_version );

        return true;
    }

    /**
     * Vide le cache d'un module spécifique.
     *
     * @since    1.1.0
     * @param    string    $module_name    Nom du module.
     * @return   bool                      True si le cache a été vidé.
     */
    public function flush_module_cache( $module_name ) {
        if ( ! in_array( $module_name, $this->cached_modules ) ) {
            return false;
        }

        $success = true;

        // Vider le cache WordPress pour ce module
        $success &= wp_cache_flush_group( 'boss_' . $module_name );

        // Supprimer les transients du module
        $success &= $this->delete_module_transients( $module_name );

        // Appeler la méthode flush du module s'il existe
        $cache_class = 'Boss_' . ucfirst( $module_name ) . '_Cache';
        if ( class_exists( $cache_class ) ) {
            // Vérifier si c'est un Singleton avec get_instance()
            if ( method_exists( $cache_class, 'get_instance' ) ) {
                $cache_instance = call_user_func( array( $cache_class, 'get_instance' ) );
                if ( method_exists( $cache_instance, 'flush' ) ) {
                    $success &= $cache_instance->flush();
                }
            } else {
                // Essayer d'instancier normalement si pas de Singleton
                try {
                    $cache_instance = new $cache_class();
                    if ( method_exists( $cache_instance, 'flush' ) ) {
                        $success &= $cache_instance->flush();
                    }
                } catch ( Exception $e ) {
                    // Si l'instanciation échoue, continuer sans erreur
                    error_log( 'Boss SEO: Impossible d\'instancier ' . $cache_class . ': ' . $e->getMessage() );
                }
            }
        }

        // Log de l'action
        error_log( 'Boss SEO: Cache du module ' . $module_name . ' vidé' );

        return $success;
    }

    /**
     * Vide tout le cache du plugin.
     *
     * @since    1.1.0
     * @return   array    Résultats du vidage par type de cache.
     */
    public function flush_all_cache() {
        $results = array(
            'assets' => false,
            'modules' => array(),
            'transients' => false,
            'third_party' => array(),
            'opcache' => false
        );

        // 1. Vider le cache des assets
        $results['assets'] = $this->force_assets_refresh();

        // 2. Vider le cache de tous les modules
        foreach ( $this->cached_modules as $module ) {
            $results['modules'][ $module ] = $this->flush_module_cache( $module );
        }

        // 3. Vider tous les transients Boss SEO
        $results['transients'] = $this->flush_all_transients();

        // 4. Vider les caches des plugins tiers
        $results['third_party'] = $this->flush_third_party_caches();

        // 5. Vider l'OpCache PHP si disponible
        $results['opcache'] = $this->flush_opcache();

        // 6. Régénérer les permaliens
        flush_rewrite_rules();

        // Log de l'action complète
        error_log( 'Boss SEO: Tous les caches vidés - Résultats: ' . print_r( $results, true ) );

        return $results;
    }

    /**
     * Vide tous les transients Boss SEO.
     *
     * @since    1.1.0
     * @return   bool    True si les transients ont été vidés.
     */
    private function flush_all_transients() {
        global $wpdb;

        try {
            $deleted = $wpdb->query(
                $wpdb->prepare(
                    "DELETE FROM {$wpdb->options}
                     WHERE option_name LIKE %s
                     OR option_name LIKE %s",
                    '_transient_boss_%',
                    '_transient_timeout_boss_%'
                )
            );

            error_log( 'Boss SEO: ' . $deleted . ' transients supprimés' );
            return $deleted !== false;
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la suppression des transients: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Supprime les transients d'un module spécifique.
     *
     * @since    1.1.0
     * @param    string    $module_name    Nom du module.
     * @return   bool                      True si les transients ont été supprimés.
     */
    private function delete_module_transients( $module_name ) {
        global $wpdb;

        try {
            $pattern = 'boss_' . $module_name . '_%';
            $deleted = $wpdb->query(
                $wpdb->prepare(
                    "DELETE FROM {$wpdb->options}
                     WHERE option_name LIKE %s
                     OR option_name LIKE %s",
                    '_transient_' . $pattern,
                    '_transient_timeout_' . $pattern
                )
            );

            return $deleted !== false;
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la suppression des transients du module ' . $module_name . ': ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Vide les caches des plugins tiers populaires.
     *
     * @since    1.1.0
     * @return   array    Liste des plugins de cache vidés.
     */
    private function flush_third_party_caches() {
        $cleared_plugins = array();

        // WP Rocket
        if ( function_exists( 'rocket_clean_domain' ) ) {
            rocket_clean_domain();
            $cleared_plugins[] = 'WP Rocket';
        }

        // W3 Total Cache
        if ( function_exists( 'w3tc_flush_all' ) ) {
            w3tc_flush_all();
            $cleared_plugins[] = 'W3 Total Cache';
        }

        // WP Super Cache
        if ( function_exists( 'wp_cache_clear_cache' ) ) {
            wp_cache_clear_cache();
            $cleared_plugins[] = 'WP Super Cache';
        }

        // LiteSpeed Cache
        if ( class_exists( 'LiteSpeed_Cache_API' ) ) {
            LiteSpeed_Cache_API::purge_all();
            $cleared_plugins[] = 'LiteSpeed Cache';
        }

        // Autoptimize
        if ( class_exists( 'autoptimizeCache' ) ) {
            autoptimizeCache::clearall();
            $cleared_plugins[] = 'Autoptimize';
        }

        // WP Fastest Cache
        if ( class_exists( 'WpFastestCache' ) ) {
            $wp_fastest_cache = new WpFastestCache();
            if ( method_exists( $wp_fastest_cache, 'deleteCache' ) ) {
                $wp_fastest_cache->deleteCache();
                $cleared_plugins[] = 'WP Fastest Cache';
            }
        }

        // Cloudflare (si configuré)
        if ( defined( 'CLOUDFLARE_API_KEY' ) && defined( 'CLOUDFLARE_EMAIL' ) ) {
            $this->flush_cloudflare_cache();
            $cleared_plugins[] = 'Cloudflare';
        }

        return $cleared_plugins;
    }

    /**
     * Vide l'OpCache PHP si disponible.
     *
     * @since    1.1.0
     * @return   bool    True si l'OpCache a été vidé.
     */
    private function flush_opcache() {
        if ( function_exists( 'opcache_reset' ) ) {
            try {
                opcache_reset();
                return true;
            } catch ( Exception $e ) {
                error_log( 'Boss SEO: Erreur lors du vidage de l\'OpCache: ' . $e->getMessage() );
                return false;
            }
        }
        return false;
    }

    /**
     * Vide le cache Cloudflare (si configuré).
     *
     * @since    1.1.0
     * @return   bool    True si le cache Cloudflare a été vidé.
     */
    private function flush_cloudflare_cache() {
        if ( ! defined( 'CLOUDFLARE_API_KEY' ) || ! defined( 'CLOUDFLARE_EMAIL' ) || ! defined( 'CLOUDFLARE_ZONE_ID' ) ) {
            return false;
        }

        $url = 'https://api.cloudflare.com/client/v4/zones/' . CLOUDFLARE_ZONE_ID . '/purge_cache';

        $response = wp_remote_post( $url, array(
            'headers' => array(
                'X-Auth-Email' => CLOUDFLARE_EMAIL,
                'X-Auth-Key' => CLOUDFLARE_API_KEY,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode( array( 'purge_everything' => true ) ),
            'timeout' => 30
        ) );

        if ( is_wp_error( $response ) ) {
            error_log( 'Boss SEO: Erreur Cloudflare: ' . $response->get_error_message() );
            return false;
        }

        $body = json_decode( wp_remote_retrieve_body( $response ), true );
        return isset( $body['success'] ) && $body['success'];
    }

    /**
     * Invalide le cache lors de la sauvegarde d'un post.
     *
     * @since    1.1.0
     * @param    int    $post_id    ID du post.
     */
    public function invalidate_post_cache( $post_id ) {
        // Invalider le cache de l'optimizer pour ce post
        $this->flush_module_cache( 'optimizer' );

        // Invalider le cache des analytics si c'est un post publié
        if ( get_post_status( $post_id ) === 'publish' ) {
            $this->flush_module_cache( 'analytics' );
        }

        // Hook personnalisé pour permettre aux autres modules de réagir
        do_action( 'boss_seo_post_cache_invalidated', $post_id );
    }

    /**
     * Invalide le cache des paramètres si nécessaire.
     *
     * @since    1.1.0
     * @param    string    $option_name     Nom de l'option.
     * @param    mixed     $old_value       Ancienne valeur.
     * @param    mixed     $new_value       Nouvelle valeur.
     */
    public function maybe_invalidate_settings_cache( $option_name, $old_value, $new_value ) {
        // Invalider le cache si c'est une option Boss SEO
        if ( strpos( $option_name, 'boss_seo' ) === 0 || strpos( $option_name, 'boss-seo' ) === 0 ) {
            $this->force_assets_refresh();

            // Hook personnalisé
            do_action( 'boss_seo_settings_cache_invalidated', $option_name, $old_value, $new_value );
        }
    }

    /**
     * Invalide le cache des assets.
     *
     * @since    1.1.0
     */
    public function invalidate_assets_cache() {
        $this->force_assets_refresh();
    }

    /**
     * Invalide tout le cache.
     *
     * @since    1.1.0
     */
    public function invalidate_all_cache() {
        $this->flush_all_cache();
    }

    /**
     * Désactive le cache en mode debug.
     *
     * @since    1.1.0
     */
    public function disable_cache_in_debug_mode() {
        // Forcer le rechargement des assets en mode debug
        $this->assets_version = time();

        // Désactiver les transients en mode debug
        add_filter( 'pre_transient_boss_seo_*', '__return_false' );
        add_filter( 'pre_site_transient_boss_seo_*', '__return_false' );
    }

    /**
     * Récupère les statistiques du cache.
     *
     * @since    1.1.0
     * @return   array    Statistiques du cache.
     */
    public function get_cache_stats() {
        global $wpdb;

        $stats = array(
            'transients_count' => 0,
            'assets_version' => $this->get_assets_version(),
            'debug_mode' => defined( 'WP_DEBUG' ) && WP_DEBUG,
            'modules_status' => array()
        );

        // Compter les transients Boss SEO
        try {
            $transients_count = $wpdb->get_var(
                "SELECT COUNT(*) FROM {$wpdb->options}
                 WHERE option_name LIKE '_transient_boss_%'"
            );
            $stats['transients_count'] = (int) $transients_count;
        } catch ( Exception $e ) {
            $stats['transients_count'] = 0;
        }

        // Vérifier le statut des modules
        foreach ( $this->cached_modules as $module ) {
            $cache_class = 'Boss_' . ucfirst( $module ) . '_Cache';
            $stats['modules_status'][ $module ] = class_exists( $cache_class );
        }

        return $stats;
    }

    /**
     * Méthode pour empêcher le clonage.
     *
     * @since    1.1.0
     */
    private function __clone() {}

    /**
     * Méthode pour empêcher la désérialisation.
     *
     * @since    1.1.0
     */
    public function __wakeup() {}
}
