<?php
/**
 * Analyseur technique Boss SEO v2.0 - Nouvelle génération
 *
 * Analyseur technique moderne avec intégration réelle Google PageSpeed API,
 * sélection de pages, et suggestions IA intelligentes.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Analyseur technique Boss SEO v2.0
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Technical_Analyzer_V2 {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance du gestionnaire PageSpeed.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_PageSpeed_Manager    $pagespeed_manager    Instance du gestionnaire PageSpeed.
     */
    private $pagespeed_manager;

    /**
     * Instance du générateur de suggestions IA.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_AI_Suggestions_Generator    $ai_generator    Instance du générateur IA.
     */
    private $ai_generator;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Charger les dépendances
        $this->load_dependencies();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.1.0
     */
    private function load_dependencies() {
        // Charger le gestionnaire PageSpeed si pas déjà fait
        if ( ! class_exists( 'Boss_PageSpeed_Manager' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-pagespeed-manager.php';
        }

        // Charger le générateur de suggestions IA
        if ( ! class_exists( 'Boss_AI_Suggestions_Generator' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-ai-suggestions-generator.php';
        }

        $this->pagespeed_manager = new Boss_PageSpeed_Manager();
        $this->ai_generator = new Boss_AI_Suggestions_Generator( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour cette classe.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Routes API REST
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ) );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.1.0
     */
    public function register_rest_routes() {
        // Route pour récupérer les pages disponibles
        register_rest_route( 'boss-seo/v2', '/technical/pages', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_available_pages' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Route pour analyser une page spécifique
        register_rest_route( 'boss-seo/v2', '/technical/analyze', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'analyze_page' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'url' => array(
                        'required'          => true,
                        'validate_callback' => array( $this, 'validate_url' ),
                        'sanitize_callback' => 'esc_url_raw',
                    ),
                    'strategy' => array(
                        'required'          => false,
                        'default'           => 'mobile',
                        'validate_callback' => array( $this, 'validate_strategy' ),
                        'sanitize_callback' => 'sanitize_text_field',
                    ),
                    'include_ai_suggestions' => array(
                        'required'          => false,
                        'default'           => true,
                        'validate_callback' => 'rest_validate_request_arg',
                        'sanitize_callback' => 'rest_sanitize_boolean',
                    ),
                ),
            ),
        ) );

        // Route pour récupérer l'historique des analyses
        register_rest_route( 'boss-seo/v2', '/technical/history', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_analysis_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'page' => array(
                        'required'          => false,
                        'default'           => 1,
                        'validate_callback' => array( $this, 'validate_page_number' ),
                        'sanitize_callback' => 'absint',
                    ),
                    'per_page' => array(
                        'required'          => false,
                        'default'           => 20,
                        'validate_callback' => array( $this, 'validate_per_page' ),
                        'sanitize_callback' => 'absint',
                    ),
                ),
            ),
        ) );

        // Route pour générer des suggestions IA pour une analyse existante
        register_rest_route( 'boss-seo/v2', '/technical/ai-suggestions', array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'generate_ai_suggestions' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'analysis_data' => array(
                        'required'          => true,
                        'validate_callback' => 'rest_validate_request_arg',
                    ),
                ),
            ),
        ) );
    }

    /**
     * Récupère la liste des pages disponibles pour l'analyse.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_available_pages( $request ) {
        try {
            $pages = array();

            // 1. Page d'accueil
            $pages[] = array(
                'id'          => 'home',
                'title'       => __( 'Page d\'accueil', 'boss-seo' ),
                'url'         => home_url(),
                'type'        => 'home',
                'priority'    => 'high',
                'description' => __( 'La page principale de votre site', 'boss-seo' ),
            );

            // 2. Pages WordPress
            $wp_pages = get_pages( array(
                'post_status' => 'publish',
                'number'      => 50,
                'sort_column' => 'menu_order,post_title',
            ) );

            foreach ( $wp_pages as $page ) {
                $pages[] = array(
                    'id'          => 'page_' . $page->ID,
                    'title'       => $page->post_title,
                    'url'         => get_permalink( $page->ID ),
                    'type'        => 'page',
                    'priority'    => 'medium',
                    'description' => wp_trim_words( $page->post_content, 20 ),
                    'post_id'     => $page->ID,
                );
            }

            // 3. Articles récents
            $recent_posts = get_posts( array(
                'post_status'    => 'publish',
                'posts_per_page' => 20,
                'orderby'        => 'date',
                'order'          => 'DESC',
            ) );

            foreach ( $recent_posts as $post ) {
                $pages[] = array(
                    'id'          => 'post_' . $post->ID,
                    'title'       => $post->post_title,
                    'url'         => get_permalink( $post->ID ),
                    'type'        => 'post',
                    'priority'    => 'medium',
                    'description' => wp_trim_words( $post->post_content, 20 ),
                    'post_id'     => $post->ID,
                    'date'        => $post->post_date,
                );
            }

            // 4. Pages de catégories principales
            $categories = get_categories( array(
                'number'     => 10,
                'orderby'    => 'count',
                'order'      => 'DESC',
                'hide_empty' => true,
            ) );

            foreach ( $categories as $category ) {
                $pages[] = array(
                    'id'          => 'category_' . $category->term_id,
                    'title'       => sprintf( __( 'Catégorie: %s', 'boss-seo' ), $category->name ),
                    'url'         => get_category_link( $category->term_id ),
                    'type'        => 'category',
                    'priority'    => 'low',
                    'description' => $category->description ?: sprintf( __( 'Page de la catégorie %s (%d articles)', 'boss-seo' ), $category->name, $category->count ),
                    'term_id'     => $category->term_id,
                );
            }

            // 5. Pages WooCommerce si disponible
            if ( class_exists( 'WooCommerce' ) ) {
                $woo_pages = array(
                    'shop'     => wc_get_page_id( 'shop' ),
                    'cart'     => wc_get_page_id( 'cart' ),
                    'checkout' => wc_get_page_id( 'checkout' ),
                    'account'  => wc_get_page_id( 'myaccount' ),
                );

                foreach ( $woo_pages as $page_type => $page_id ) {
                    if ( $page_id && $page_id > 0 ) {
                        $page = get_post( $page_id );
                        if ( $page ) {
                            $pages[] = array(
                                'id'          => 'woo_' . $page_type,
                                'title'       => sprintf( __( 'WooCommerce: %s', 'boss-seo' ), $page->post_title ),
                                'url'         => get_permalink( $page_id ),
                                'type'        => 'woocommerce',
                                'priority'    => 'high',
                                'description' => sprintf( __( 'Page %s de WooCommerce', 'boss-seo' ), $page_type ),
                                'post_id'     => $page_id,
                            );
                        }
                    }
                }
            }

            // Trier par priorité et titre
            usort( $pages, function( $a, $b ) {
                $priority_order = array( 'high' => 1, 'medium' => 2, 'low' => 3 );
                $a_priority = $priority_order[ $a['priority'] ] ?? 4;
                $b_priority = $priority_order[ $b['priority'] ] ?? 4;

                if ( $a_priority === $b_priority ) {
                    return strcmp( $a['title'], $b['title'] );
                }

                return $a_priority - $b_priority;
            } );

            return rest_ensure_response( array(
                'success' => true,
                'pages'   => $pages,
                'total'   => count( $pages ),
                'message' => sprintf( __( '%d pages disponibles pour l\'analyse', 'boss-seo' ), count( $pages ) ),
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'pages_fetch_error',
                __( 'Erreur lors de la récupération des pages: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Analyse une page spécifique avec PageSpeed Insights et génère des suggestions IA.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function analyze_page( $request ) {
        $url = $request->get_param( 'url' );
        $strategy = $request->get_param( 'strategy' );
        $include_ai_suggestions = $request->get_param( 'include_ai_suggestions' );

        try {
            // Vérifier que l'API PageSpeed est configurée
            if ( ! $this->is_pagespeed_api_configured() ) {
                return new WP_Error(
                    'api_not_configured',
                    __( 'L\'API Google PageSpeed Insights n\'est pas configurée. Veuillez configurer votre clé API dans Paramètres > Services externes.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            // Démarrer l'analyse
            $analysis_start_time = microtime( true );

            // 1. Analyse PageSpeed Insights
            $pagespeed_data = $this->pagespeed_manager->analyze_url( $url, $strategy );

            if ( ! $pagespeed_data || isset( $pagespeed_data['error'] ) ) {
                return new WP_Error(
                    'pagespeed_analysis_failed',
                    __( 'L\'analyse PageSpeed Insights a échoué: ', 'boss-seo' ) . ( $pagespeed_data['error'] ?? __( 'Erreur inconnue', 'boss-seo' ) ),
                    array( 'status' => 500 )
                );
            }

            // 2. Analyse technique complémentaire
            $technical_analysis = $this->perform_technical_analysis( $url );

            // 3. Combiner les données
            $combined_analysis = $this->combine_analysis_data( $pagespeed_data, $technical_analysis );

            // 4. Générer des suggestions IA si demandé
            $ai_suggestions = array();
            if ( $include_ai_suggestions ) {
                $ai_suggestions = $this->ai_generator->generate_suggestions( $combined_analysis, $url );
            }

            // 5. Calculer le score global
            $global_score = $this->calculate_global_score( $combined_analysis );

            // 6. Préparer les résultats finaux
            $analysis_results = array(
                'id'                => 'analysis_' . time(),
                'url'               => $url,
                'strategy'          => $strategy,
                'date'              => current_time( 'mysql' ),
                'analysis_duration' => round( ( microtime( true ) - $analysis_start_time ), 2 ),
                'global_score'      => $global_score,
                'pagespeed_data'    => $pagespeed_data,
                'technical_data'    => $technical_analysis,
                'combined_analysis' => $combined_analysis,
                'ai_suggestions'    => $ai_suggestions,
                'categories'        => $this->categorize_issues( $combined_analysis ),
                'summary'           => $this->generate_analysis_summary( $combined_analysis, $global_score ),
            );

            // 7. Sauvegarder l'analyse dans l'historique
            $this->save_analysis_to_history( $analysis_results );

            return rest_ensure_response( array(
                'success' => true,
                'message' => sprintf(
                    __( 'Analyse terminée en %s secondes avec un score de %d/100', 'boss-seo' ),
                    $analysis_results['analysis_duration'],
                    $global_score
                ),
                'data'    => $analysis_results,
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'analysis_error',
                __( 'Erreur lors de l\'analyse: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Effectue une analyse technique complémentaire.
     *
     * @since    1.1.0
     * @param    string    $url    URL à analyser.
     * @return   array             Données d'analyse technique.
     */
    private function perform_technical_analysis( $url ) {
        $analysis = array(
            'meta_tags'      => $this->analyze_meta_tags( $url ),
            'headings'       => $this->analyze_headings( $url ),
            'images'         => $this->analyze_images( $url ),
            'links'          => $this->analyze_links( $url ),
            'schema_markup'  => $this->analyze_schema_markup( $url ),
            'security'       => $this->analyze_security( $url ),
            'accessibility'  => $this->analyze_accessibility( $url ),
        );

        return $analysis;
    }

    /**
     * Analyse les balises meta d'une page.
     *
     * @since    1.1.0
     * @param    string    $url    URL à analyser.
     * @return   array             Données des balises meta.
     */
    private function analyze_meta_tags( $url ) {
        $html = $this->fetch_page_content( $url );
        if ( ! $html ) {
            return array( 'error' => __( 'Impossible de récupérer le contenu de la page', 'boss-seo' ) );
        }

        $dom = new DOMDocument();
        @$dom->loadHTML( $html );
        $xpath = new DOMXPath( $dom );

        $meta_analysis = array(
            'title' => array(
                'content' => '',
                'length'  => 0,
                'status'  => 'missing',
                'issues'  => array(),
            ),
            'description' => array(
                'content' => '',
                'length'  => 0,
                'status'  => 'missing',
                'issues'  => array(),
            ),
            'keywords' => array(
                'content' => '',
                'status'  => 'missing',
            ),
            'robots' => array(
                'content' => '',
                'status'  => 'missing',
            ),
            'canonical' => array(
                'url'    => '',
                'status' => 'missing',
            ),
            'og_tags' => array(),
            'twitter_tags' => array(),
        );

        // Analyser le titre
        $title_nodes = $xpath->query( '//title' );
        if ( $title_nodes->length > 0 ) {
            $title_content = trim( $title_nodes->item( 0 )->textContent );
            $meta_analysis['title'] = array(
                'content' => $title_content,
                'length'  => strlen( $title_content ),
                'status'  => $this->evaluate_title_status( $title_content ),
                'issues'  => $this->get_title_issues( $title_content ),
            );
        }

        // Analyser la meta description
        $description_nodes = $xpath->query( '//meta[@name="description"]' );
        if ( $description_nodes->length > 0 ) {
            $description_content = trim( $description_nodes->item( 0 )->getAttribute( 'content' ) );
            $meta_analysis['description'] = array(
                'content' => $description_content,
                'length'  => strlen( $description_content ),
                'status'  => $this->evaluate_description_status( $description_content ),
                'issues'  => $this->get_description_issues( $description_content ),
            );
        }

        // Analyser les autres balises meta
        $meta_nodes = $xpath->query( '//meta' );
        foreach ( $meta_nodes as $meta ) {
            $name = $meta->getAttribute( 'name' );
            $property = $meta->getAttribute( 'property' );
            $content = $meta->getAttribute( 'content' );

            if ( $name === 'keywords' ) {
                $meta_analysis['keywords'] = array(
                    'content' => $content,
                    'status'  => ! empty( $content ) ? 'present' : 'missing',
                );
            } elseif ( $name === 'robots' ) {
                $meta_analysis['robots'] = array(
                    'content' => $content,
                    'status'  => ! empty( $content ) ? 'present' : 'missing',
                );
            } elseif ( strpos( $property, 'og:' ) === 0 ) {
                $meta_analysis['og_tags'][ str_replace( 'og:', '', $property ) ] = $content;
            } elseif ( strpos( $name, 'twitter:' ) === 0 ) {
                $meta_analysis['twitter_tags'][ str_replace( 'twitter:', '', $name ) ] = $content;
            }
        }

        // Analyser le canonical
        $canonical_nodes = $xpath->query( '//link[@rel="canonical"]' );
        if ( $canonical_nodes->length > 0 ) {
            $canonical_url = $canonical_nodes->item( 0 )->getAttribute( 'href' );
            $meta_analysis['canonical'] = array(
                'url'    => $canonical_url,
                'status' => ! empty( $canonical_url ) ? 'present' : 'missing',
            );
        }

        return $meta_analysis;
    }
