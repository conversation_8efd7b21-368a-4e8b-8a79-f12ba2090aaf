<?php
/**
 * Provide a admin area view for the plugin - Technical Analysis v2.0
 *
 * This file is used to markup the admin-facing aspects of the plugin.
 * Version 2.0 with real Google PageSpeed API integration and AI suggestions.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin/partials
 */

// Charger l'intégration du nouveau module
if ( ! class_exists( 'Boss_Technical_Analysis_Integration' ) ) {
    require_once plugin_dir_path( dirname( dirname( __FILE__ ) ) ) . 'includes/class-boss-technical-analysis-integration.php';
}

// Initialiser l'intégration
$integration = new Boss_Technical_Analysis_Integration( 'boss-seo', '1.1.0' );
$integration->register_hooks();

// Vérifier si le nouveau module est activé
$v2_enabled = get_option( 'boss_seo_technical_analysis_v2_enabled', false );
$v1_disabled = get_option( 'boss_seo_technical_analysis_v1_disabled', false );

?>

<div class="wrap">
    <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>

    <?php if ( $v2_enabled && $v1_disabled ) : ?>
        <!-- Nouveau module v2.0 avec API réelle -->
        <div class="notice notice-success">
            <p>
                <strong><?php _e( 'Nouveau !', 'boss-seo' ); ?></strong>
                <?php _e( 'Module d\'analyse technique v2.0 avec intégration réelle Google PageSpeed API et suggestions IA.', 'boss-seo' ); ?>
            </p>
        </div>

        <?php
        // Vérifier la configuration API
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $api_key = isset( $external_services['google_pagespeed']['api_key'] ) ? $external_services['google_pagespeed']['api_key'] : '';

        if ( empty( $api_key ) ) {
            $api_key = get_option( 'boss_optimizer_pagespeed_api_key', '' );
        }

        if ( empty( $api_key ) ) :
        ?>
            <div class="notice notice-warning">
                <p>
                    <strong><?php _e( 'Configuration requise', 'boss-seo' ); ?></strong><br>
                    <?php _e( 'Pour utiliser l\'analyse technique avec des données réelles, vous devez configurer votre clé API Google PageSpeed Insights.', 'boss-seo' ); ?>
                </p>
                <p>
                    <a href="<?php echo admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ); ?>" class="button button-primary">
                        <?php _e( 'Configurer l\'API PageSpeed', 'boss-seo' ); ?>
                    </a>
                    <a href="https://developers.google.com/speed/docs/insights/v5/get-started" target="_blank" class="button button-secondary">
                        <?php _e( 'Obtenir une clé API', 'boss-seo' ); ?>
                    </a>
                </p>
            </div>
        <?php endif; ?>

        <!-- Container pour le nouveau module React -->
        <div id="boss-seo-technical-analysis-v2-app"></div>

        <noscript>
            <div class="notice notice-error">
                <p><?php _e( 'JavaScript est requis pour utiliser le module d\'analyse technique Boss SEO.', 'boss-seo' ); ?></p>
            </div>
        </noscript>

    <?php else : ?>
        <!-- Fallback vers l'ancien module ou migration -->
        <div class="notice notice-info">
            <p>
                <strong><?php _e( 'Migration disponible', 'boss-seo' ); ?></strong><br>
                <?php _e( 'Un nouveau module d\'analyse technique avec intégration réelle Google PageSpeed API est disponible.', 'boss-seo' ); ?>
            </p>
            <p>
                <button type="button" class="button button-primary" onclick="migrateTechnicalModule()">
                    <?php _e( 'Migrer vers v2.0', 'boss-seo' ); ?>
                </button>
            </p>
        </div>

        <!-- Ancien module (avec données fictives) -->
        <div id="boss-seo-technical-app"></div>

        <script>
        function migrateTechnicalModule() {
            if (confirm('<?php _e( "Voulez-vous migrer vers le nouveau module d\'analyse technique v2.0 avec API réelle ?", "boss-seo" ); ?>')) {
                // Activer le nouveau module
                fetch('<?php echo admin_url( "admin-ajax.php" ); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'boss_seo_migrate_technical_v2',
                        nonce: '<?php echo wp_create_nonce( "boss_seo_migrate_technical_v2" ); ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('<?php _e( "Migration réussie ! La page va se recharger.", "boss-seo" ); ?>');
                        location.reload();
                    } else {
                        alert('<?php _e( "Erreur lors de la migration: ", "boss-seo" ); ?>' + data.data);
                    }
                })
                .catch(error => {
                    alert('<?php _e( "Erreur de connexion lors de la migration.", "boss-seo" ); ?>');
                });
            }
        }
        </script>
    <?php endif; ?>
</div>

<?php
// Ajouter les hooks pour charger les assets du nouveau module
add_action( 'admin_footer', function() use ( $v2_enabled, $v1_disabled ) {
    if ( $v2_enabled && $v1_disabled ) {
        // Charger les assets du nouveau module
        wp_enqueue_script(
            'boss-seo-technical-analysis-v2',
            plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'dist/technical-analysis-v2.js',
            array( 'wp-element', 'wp-components', 'wp-i18n', 'wp-api-fetch' ),
            '1.1.0',
            true
        );

        wp_enqueue_style(
            'boss-seo-technical-analysis-v2',
            plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'dist/technical-analysis-v2.css',
            array( 'wp-components' ),
            '1.1.0'
        );

        // Configuration JavaScript
        wp_localize_script(
            'boss-seo-technical-analysis-v2',
            'bossSeoTechnicalV2',
            array(
                'apiUrl'       => rest_url( 'boss-seo/v2/' ),
                'nonce'        => wp_create_nonce( 'wp_rest' ),
                'siteUrl'      => home_url(),
                'version'      => '2.0',
                'strings'      => array(
                    'analyzing'       => __( 'Analyse en cours...', 'boss-seo' ),
                    'analyzeComplete' => __( 'Analyse terminée', 'boss-seo' ),
                    'analyzeError'    => __( 'Erreur lors de l\'analyse', 'boss-seo' ),
                    'selectPage'      => __( 'Sélectionnez une page', 'boss-seo' ),
                    'configureApi'    => __( 'Configurer l\'API', 'boss-seo' ),
                ),
            )
        );

        // Initialiser le module React
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (window.wp && window.wp.element && document.getElementById('boss-seo-technical-analysis-v2-app')) {
                // Le composant React sera chargé automatiquement
                console.log('Boss SEO Technical Analysis v2.0 initialized');
            }
        });
        </script>
        <?php
    }
} );
?>
