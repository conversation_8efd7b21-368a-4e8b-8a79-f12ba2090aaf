/**
 * Module d'Analyse Technique Boss SEO v2.0 - Nouvelle génération
 *
 * Interface moderne avec intégration réelle Google PageSpeed API,
 * sélection de pages, et suggestions IA intelligentes.
 */

import React, { useState, useEffect } from 'react';
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    SelectControl,
    Spinner,
    Notice,
    TabPanel,
    Flex,
    FlexItem,
    __experimentalSpacer as Spacer,
    __experimentalHeading as Heading,
    __experimentalText as Text,
    ProgressBar,
    Badge,
    Icon
} from '@wordpress/components';
import { __ } from '@wordpress/i18n';
import apiFetch from '@wordpress/api-fetch';
import {
    desktop,
    mobile,
    search,
    performance,
    warning,
    check,
    lightbulb,
    chartLine,
    external
} from '@wordpress/icons';

const TechnicalAnalysisV2 = () => {
    // États principaux
    const [availablePages, setAvailablePages] = useState([]);
    const [selectedPage, setSelectedPage] = useState('');
    const [strategy, setStrategy] = useState('mobile');
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [analysisResults, setAnalysisResults] = useState(null);
    const [analysisHistory, setAnalysisHistory] = useState([]);
    const [notices, setNotices] = useState([]);
    const [activeTab, setActiveTab] = useState('analyzer');

    // Charger les données initiales
    useEffect(() => {
        loadAvailablePages();
        loadAnalysisHistory();
    }, []);

    /**
     * Charge la liste des pages disponibles
     */
    const loadAvailablePages = async () => {
        try {
            const response = await apiFetch({
                path: '/boss-seo/v2/technical/pages',
                method: 'GET'
            });

            if (response.success) {
                setAvailablePages(response.pages);
                // Sélectionner la page d'accueil par défaut
                const homePage = response.pages.find(page => page.type === 'home');
                if (homePage) {
                    setSelectedPage(homePage.url);
                }
            }
        } catch (error) {
            console.error('Erreur lors du chargement des pages:', error);
            addNotice('error', __('Impossible de charger la liste des pages.', 'boss-seo'));
        }
    };

    /**
     * Charge l'historique des analyses
     */
    const loadAnalysisHistory = async () => {
        try {
            const response = await apiFetch({
                path: '/boss-seo/v2/technical/history',
                method: 'GET'
            });

            if (response.success) {
                setAnalysisHistory(response.data || []);
            }
        } catch (error) {
            console.error('Erreur lors du chargement de l\'historique:', error);
        }
    };

    /**
     * Ajoute une notification
     */
    const addNotice = (type, message) => {
        const notice = {
            id: Date.now(),
            type,
            message
        };
        setNotices(prev => [...prev, notice]);

        // Supprimer automatiquement après 5 secondes
        setTimeout(() => {
            setNotices(prev => prev.filter(n => n.id !== notice.id));
        }, 5000);
    };

    /**
     * Lance l'analyse d'une page
     */
    const analyzeSelectedPage = async () => {
        if (!selectedPage) {
            addNotice('error', __('Veuillez sélectionner une page à analyser.', 'boss-seo'));
            return;
        }

        setIsAnalyzing(true);
        setAnalysisResults(null);

        try {
            const response = await apiFetch({
                path: '/boss-seo/v2/technical/analyze',
                method: 'POST',
                data: {
                    url: selectedPage,
                    strategy: strategy,
                    include_ai_suggestions: true
                }
            });

            if (response.success) {
                setAnalysisResults(response.data);
                addNotice('success', response.message);
                // Recharger l'historique
                loadAnalysisHistory();
            } else {
                addNotice('error', response.message || __('Erreur lors de l\'analyse.', 'boss-seo'));
            }
        } catch (error) {
            console.error('Erreur lors de l\'analyse:', error);
            if (error.message.includes('api_not_configured')) {
                addNotice('error', __('L\'API Google PageSpeed Insights n\'est pas configurée. Veuillez configurer votre clé API dans Paramètres > Services externes.', 'boss-seo'));
            } else {
                addNotice('error', __('Erreur lors de l\'analyse de la page.', 'boss-seo'));
            }
        } finally {
            setIsAnalyzing(false);
        }
    };

    /**
     * Supprime une notification
     */
    const removeNotice = (noticeId) => {
        setNotices(prev => prev.filter(n => n.id !== noticeId));
    };

    /**
     * Formate le score avec couleur
     */
    const getScoreColor = (score) => {
        if (score >= 90) return '#0f5132'; // Vert foncé
        if (score >= 50) return '#664d03'; // Orange foncé
        return '#842029'; // Rouge foncé
    };

    /**
     * Formate le statut des Core Web Vitals
     */
    const getVitalStatus = (status) => {
        const statusMap = {
            'good': { color: '#0f5132', label: __('Bon', 'boss-seo') },
            'needs-improvement': { color: '#664d03', label: __('À améliorer', 'boss-seo') },
            'poor': { color: '#842029', label: __('Mauvais', 'boss-seo') }
        };
        return statusMap[status] || { color: '#6c757d', label: __('Inconnu', 'boss-seo') };
    };

    /**
     * Rendu du sélecteur de pages
     */
    const renderPageSelector = () => {
        const pageOptions = availablePages.map(page => ({
            label: `${page.title} (${page.type})`,
            value: page.url
        }));

        return (
            <Card>
                <CardHeader>
                    <Heading level={3}>
                        <Icon icon={search} /> {__('Sélection de la Page', 'boss-seo')}
                    </Heading>
                </CardHeader>
                <CardBody>
                    <Flex gap={4} align="end">
                        <FlexItem isBlock>
                            <SelectControl
                                label={__('Page à analyser', 'boss-seo')}
                                value={selectedPage}
                                options={[
                                    { label: __('Sélectionnez une page...', 'boss-seo'), value: '' },
                                    ...pageOptions
                                ]}
                                onChange={setSelectedPage}
                                help={__('Choisissez la page que vous souhaitez analyser avec PageSpeed Insights.', 'boss-seo')}
                            />
                        </FlexItem>
                        <FlexItem>
                            <SelectControl
                                label={__('Appareil', 'boss-seo')}
                                value={strategy}
                                options={[
                                    { label: __('Mobile', 'boss-seo'), value: 'mobile' },
                                    { label: __('Desktop', 'boss-seo'), value: 'desktop' }
                                ]}
                                onChange={setStrategy}
                            />
                        </FlexItem>
                        <FlexItem>
                            <Button
                                variant="primary"
                                onClick={analyzeSelectedPage}
                                disabled={!selectedPage || isAnalyzing}
                                icon={isAnalyzing ? <Spinner /> : (strategy === 'mobile' ? mobile : desktop)}
                            >
                                {isAnalyzing
                                    ? __('Analyse en cours...', 'boss-seo')
                                    : __('Analyser', 'boss-seo')
                                }
                            </Button>
                        </FlexItem>
                    </Flex>

                    {selectedPage && (
                        <>
                            <Spacer marginY={3} />
                            <Text variant="muted">
                                <Icon icon={external} />
                                <strong>{__('URL sélectionnée:', 'boss-seo')}</strong> {selectedPage}
                            </Text>
                        </>
                    )}
                </CardBody>
            </Card>
        );
    };

    /**
     * Rendu des résultats d'analyse
     */
    const renderAnalysisResults = () => {
        if (!analysisResults) return null;

        const { global_score, pagespeed_data, ai_suggestions, analysis_duration } = analysisResults;

        return (
            <div className="analysis-results">
                <Spacer marginY={4} />

                {/* Score global */}
                <Card>
                    <CardHeader>
                        <Flex justify="space-between" align="center">
                            <Heading level={3}>
                                <Icon icon={performance} /> {__('Score Global', 'boss-seo')}
                            </Heading>
                            <Badge style={{ backgroundColor: getScoreColor(global_score), color: 'white' }}>
                                {global_score}/100
                            </Badge>
                        </Flex>
                    </CardHeader>
                    <CardBody>
                        <ProgressBar value={global_score} />
                        <Spacer marginY={2} />
                        <Text variant="muted">
                            {__('Analyse terminée en', 'boss-seo')} {analysis_duration}s
                        </Text>
                    </CardBody>
                </Card>

                <Spacer marginY={4} />

                {/* Scores par catégorie */}
                {pagespeed_data.scores && (
                    <Card>
                        <CardHeader>
                            <Heading level={3}>
                                <Icon icon={chartLine} /> {__('Scores par Catégorie', 'boss-seo')}
                            </Heading>
                        </CardHeader>
                        <CardBody>
                            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
                                {Object.entries(pagespeed_data.scores).map(([category, score]) => (
                                    <div key={category} style={{
                                        padding: '16px',
                                        border: '1px solid #ddd',
                                        borderRadius: '4px',
                                        textAlign: 'center'
                                    }}>
                                        <Text style={{
                                            fontSize: '24px',
                                            fontWeight: 'bold',
                                            color: getScoreColor(score)
                                        }}>
                                            {score}
                                        </Text>
                                        <br />
                                        <Text variant="muted" style={{ textTransform: 'capitalize' }}>
                                            {category === 'performance' && __('Performance', 'boss-seo')}
                                            {category === 'seo' && __('SEO', 'boss-seo')}
                                            {category === 'accessibility' && __('Accessibilité', 'boss-seo')}
                                            {category === 'best-practices' && __('Bonnes Pratiques', 'boss-seo')}
                                        </Text>
                                    </div>
                                ))}
                            </div>
                        </CardBody>
                    </Card>
                )}

                <Spacer marginY={4} />

                {/* Core Web Vitals */}
                {pagespeed_data.core_web_vitals && (
                    <Card>
                        <CardHeader>
                            <Heading level={3}>
                                <Icon icon={performance} /> {__('Core Web Vitals', 'boss-seo')}
                            </Heading>
                        </CardHeader>
                        <CardBody>
                            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
                                {Object.entries(pagespeed_data.core_web_vitals).map(([metric, data]) => {
                                    const vitalStatus = getVitalStatus(data.status);
                                    return (
                                        <div key={metric} style={{
                                            padding: '16px',
                                            border: '1px solid #ddd',
                                            borderRadius: '4px'
                                        }}>
                                            <Flex justify="space-between" align="center">
                                                <div>
                                                    <Text style={{ fontWeight: 'bold' }}>
                                                        {data.name}
                                                    </Text>
                                                    <br />
                                                    <Text variant="muted" style={{ fontSize: '12px' }}>
                                                        {data.description}
                                                    </Text>
                                                </div>
                                                <div style={{ textAlign: 'right' }}>
                                                    <Text style={{
                                                        fontSize: '18px',
                                                        fontWeight: 'bold',
                                                        color: vitalStatus.color
                                                    }}>
                                                        {data.value}{data.unit}
                                                    </Text>
                                                    <br />
                                                    <Badge style={{
                                                        backgroundColor: vitalStatus.color,
                                                        color: 'white',
                                                        fontSize: '10px'
                                                    }}>
                                                        {vitalStatus.label}
                                                    </Badge>
                                                </div>
                                            </Flex>
                                        </div>
                                    );
                                })}
                            </div>
                        </CardBody>
                    </Card>
                )}

                <Spacer marginY={4} />

                {/* Suggestions IA */}
                {ai_suggestions && ai_suggestions.performance && ai_suggestions.performance.length > 0 && (
                    <Card>
                        <CardHeader>
                            <Heading level={3}>
                                <Icon icon={lightbulb} /> {__('Suggestions IA', 'boss-seo')}
                            </Heading>
                        </CardHeader>
                        <CardBody>
                            {ai_suggestions.performance.map((suggestion, index) => (
                                <div key={index} style={{
                                    marginBottom: '16px',
                                    padding: '16px',
                                    backgroundColor: '#f8f9fa',
                                    borderRadius: '4px',
                                    borderLeft: `4px solid ${suggestion.priority === 'high' ? '#dc3545' : suggestion.priority === 'medium' ? '#ffc107' : '#28a745'}`
                                }}>
                                    <Flex justify="space-between" align="start">
                                        <div style={{ flex: 1 }}>
                                            <Text style={{ fontWeight: 'bold', marginBottom: '8px' }}>
                                                {suggestion.title}
                                            </Text>
                                            <Text style={{ marginBottom: '8px' }}>
                                                <strong>{__('Impact:', 'boss-seo')}</strong> {suggestion.impact}
                                            </Text>
                                            <Text>
                                                <strong>{__('Action:', 'boss-seo')}</strong> {suggestion.action}
                                            </Text>
                                        </div>
                                        <Badge style={{
                                            backgroundColor: suggestion.priority === 'high' ? '#dc3545' : suggestion.priority === 'medium' ? '#ffc107' : '#28a745',
                                            color: 'white'
                                        }}>
                                            {suggestion.priority === 'high' && __('Haute', 'boss-seo')}
                                            {suggestion.priority === 'medium' && __('Moyenne', 'boss-seo')}
                                            {suggestion.priority === 'low' && __('Basse', 'boss-seo')}
                                        </Badge>
                                    </Flex>
                                </div>
                            ))}
                        </CardBody>
                    </Card>
                )}
            </div>
        );
    };

    return (
        <div className="boss-technical-analysis-v2" id="boss-seo-technical-analysis-v2-app">
            {/* Notifications */}
            {notices.map(notice => (
                <Notice
                    key={notice.id}
                    status={notice.type}
                    onRemove={() => removeNotice(notice.id)}
                    isDismissible
                >
                    {notice.message}
                </Notice>
            ))}

            {/* En-tête */}
            <div style={{ marginBottom: '24px' }}>
                <Heading level={1}>
                    {__('Analyse Technique Avancée', 'boss-seo')}
                </Heading>
                <Text variant="muted">
                    {__('Analysez vos pages avec Google PageSpeed Insights et obtenez des suggestions IA personnalisées.', 'boss-seo')}
                </Text>
            </div>

            {/* Sélecteur de pages */}
            {renderPageSelector()}

            {/* Résultats d'analyse */}
            {renderAnalysisResults()}

            {/* Message si pas de résultats */}
            {!analysisResults && !isAnalyzing && (
                <Card style={{ marginTop: '24px', textAlign: 'center', padding: '48px' }}>
                    <CardBody>
                        <Icon icon={search} style={{ fontSize: '48px', color: '#6c757d', marginBottom: '16px' }} />
                        <Heading level={3} style={{ color: '#6c757d' }}>
                            {__('Prêt pour l\'analyse', 'boss-seo')}
                        </Heading>
                        <Text variant="muted">
                            {__('Sélectionnez une page ci-dessus et cliquez sur "Analyser" pour commencer l\'analyse technique avec PageSpeed Insights.', 'boss-seo')}
                        </Text>
                    </CardBody>
                </Card>
            )}
        </div>
    );
};

export default TechnicalAnalysisV2;
